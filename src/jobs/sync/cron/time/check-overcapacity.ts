import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { convertCronToUTC } from '@/lib/utils';
import { getAllPTOTimes } from '@/server-actions/time-reporting/helpers/pto-times';
import { getPTOTypeName } from '@/lib/pto-helpers';
import { getGlobalSettings } from '@/server-actions/globals/get-global-settings';

// Define the structure for categorized task estimates
type CategorizedEstimates = {
  regular: number;
  vacation: number; // Ferien
  sick: number; // Krankheit
  compensation: number; // Kompensation
  training: number; // Weiterbildung
  military: number; // Militär/ZS
  accident: number; // Unfall
  parental: number; // Mutterschaft/Vaterschaft
  holiday: number; // Feiertag
  other: number; // Sonstige
  freetime: number; // Freizeit
};

// Helper function to map PTO type names to category keys
function getPTOCategory(ptoTypeName: string | false): keyof CategorizedEstimates | 'regular' {
  if (!ptoTypeName) return 'regular';

  switch (ptoTypeName) {
    case 'Ferien':
      return 'vacation';
    case 'Krankheit':
      return 'sick';
    case 'Kompensation':
      return 'compensation';
    case 'Weiterbildung':
      return 'training';
    case 'Militär/ZS':
      return 'military';
    case 'Unfall':
      return 'accident';
    case 'Mutterschaft/Vaterschaft':
      return 'parental';
    case 'Feiertag':
      return 'holiday';
    case 'Sonstige':
      return 'other';
    case 'Freizeit':
      return 'freetime';
    default:
      return 'regular';
  }
}

// Helper function to create empty categorized estimates
function createEmptyCategorizedEstimates(): CategorizedEstimates {
  return {
    regular: 0,
    vacation: 0,
    sick: 0,
    compensation: 0,
    training: 0,
    military: 0,
    accident: 0,
    parental: 0,
    holiday: 0,
    other: 0,
    freetime: 0,
  };
}

// Vorlagen, SOP, Sandbox, Agency Management, Marketing Team Operations
const IGNORED_SPACES = [90152928450, 90100131196, 90100201833, 90152721646, 90152721648];

export const checkOvercapacity = client.defineJob({
  id: 'check-overcapacity',
  name: 'Check Overcapacity',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 15 * * *') }), // every day at 15:00
  run: async (payload, io, _) => {
    const overcapacitySettings = await io.runTask('fetch-overcapacity-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employee_capacity_settings').select('*');

      if (error) {
        throw new Error(`Error fetching overcapacity settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const workingHoursByEmployeeByDate = await io.runTask('fetch-working-hours-next-180-days', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('working_hours')
        .select('*, employees(*)')
        .gte('date', new Date().toISOString().slice(0, 10))
        .lte('date', new Date(new Date().setDate(new Date().getDate() + 181)).toISOString().slice(0, 10));

      if (error) {
        throw new Error(`Error fetching working hours: ${JSON.stringify(error)}`);
      }

      // group by employee and date
      return data?.reduce(
        (acc, curr) => {
          const name = String(curr.employees?.name);
          if (!acc[name]) {
            acc[name] = {};
          }
          acc[name][curr.date] = curr.hours;
          return acc;
        },
        {} as { [key: string]: { [key: string]: number } },
      );
    });

    const globalSettings = await io.runTask('fetch-global-settings', async () => {
      const settings = await getGlobalSettings();
      if (!settings.data?.pto_list_id) {
        throw new Error('PTO list ID not found in global settings');
      }
      return settings.data;
    });

    const ptoTasksNext180Days = await io.runTask('fetch-pto-tasks-next-180-days', async () => {
      const allPtoTasks = await getAllPTOTimes(String(globalSettings.pto_list_id));
      const startDate = new Date().getTime();
      const endDate = new Date(new Date().setDate(new Date().getDate() + 180)).getTime();

      return allPtoTasks.filter((task) => {
        if (!task.start_date || !task.due_date) return false;
        return Number(task.start_date) >= startDate && Number(task.start_date) <= endDate;
      });
    });

    const estimatedOpenTasks = await io.runTask('fetch-estimated-tasks-next-180-days', async () => {
      const supabase = createClient();
      // fetch all open tasks with clickup_time_estimate > 0 and clickup_due_date in the next 180 days
      // exclude tasks from ignored spaces
      const { data, error } = await supabase
        .from('tasks')
        .select(
          'clickup_task_id, clickup_time_estimate, clickup_due_date, clickup_space_id, projects(name), employees(name, user_id, clickup_user_id)',
        )
        .gt('clickup_time_estimate', 0)
        .is('clickup_date_closed', null)
        .gte('clickup_due_date', new Date().getTime())
        .lte('clickup_due_date', new Date(new Date().setDate(new Date().getDate() + 180)).getTime())
        .not('clickup_space_id', 'in', `(${IGNORED_SPACES.join(',')})`);

      if (error) {
        throw new Error(`Error fetching tasks: ${JSON.stringify(error)}`);
      }

      // Process regular tasks (non-PTO)
      const regularTasksEstimates =
        data?.reduce(
          (acc, curr) => {
            if (ptoTasksNext180Days.some((task) => task.id === curr.clickup_task_id)) return acc;
            // Additional safety check: skip tasks from ignored spaces
            if (curr.clickup_space_id && IGNORED_SPACES.includes(curr.clickup_space_id)) {
              console.log('ERROR: THIS SHOULD NEVER HAPPEN');
              return acc;
            }
            // Skip OKRs tasks in the Kundenportal space
            if (curr.projects?.name === 'OKRs' && curr.clickup_space_id === 90152721673) {
              return acc;
            }
            const startDate = new Date(curr.clickup_due_date!);
            const endDate = new Date(curr.clickup_due_date! + curr.clickup_time_estimate!);
            const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            for (let i = 0; i < days; i++) {
              const date = new Date(startDate.getTime() + i * (1000 * 60 * 60 * 24));
              const dateStr = date.toISOString().slice(0, 10);
              if (!acc[dateStr]) {
                acc[dateStr] = {};
              }
              const name = String(curr.employees?.name);
              if (!acc[dateStr][name]) {
                acc[dateStr][name] = createEmptyCategorizedEstimates();
              }
              acc[dateStr][name].regular += curr.clickup_time_estimate! / days / 1000 / 60 / 60;
            }
            return acc;
          },
          {} as { [key: string]: { [key: string]: CategorizedEstimates } },
        ) || {};

      // Process PTO tasks
      const ptoTasksEstimates = ptoTasksNext180Days.reduce(
        (acc, curr) => {
          if (!curr.start_date || !curr.due_date || !curr.time_estimate) return acc;

          const startDate = new Date(Number(curr.start_date));
          const endDate = new Date(Number(curr.due_date));
          const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) || 1;

          // Get PTO type for this task
          const ptoTypeName = getPTOTypeName(curr);
          const ptoCategory = getPTOCategory(ptoTypeName);

          for (let i = 0; i < days; i++) {
            const date = new Date(startDate.getTime() + i * (1000 * 60 * 60 * 24));
            const dateStr = date.toISOString().slice(0, 10);

            // Process each assignee
            for (const assignee of curr.assignees) {
              const name = String(assignee.username);

              if (!acc[dateStr]) {
                acc[dateStr] = {};
              }
              if (!acc[dateStr][name]) {
                acc[dateStr][name] = createEmptyCategorizedEstimates();
              }

              // Add estimate to the appropriate PTO category
              const estimatePerDayPerAssignee = curr.time_estimate / days / curr.assignees.length / 1000 / 60 / 60;
              acc[dateStr][name][ptoCategory] += estimatePerDayPerAssignee;
            }
          }
          return acc;
        },
        {} as { [key: string]: { [key: string]: CategorizedEstimates } },
      );

      // Merge regular tasks and PTO tasks estimates
      const mergedEstimates: { [key: string]: { [key: string]: CategorizedEstimates } } = {};

      // Add regular tasks
      for (const [dateStr, employees] of Object.entries(regularTasksEstimates)) {
        if (!mergedEstimates[dateStr]) {
          mergedEstimates[dateStr] = {};
        }
        for (const [employeeName, estimates] of Object.entries(employees)) {
          mergedEstimates[dateStr][employeeName] = { ...estimates };
        }
      }

      // Add PTO tasks
      for (const [dateStr, employees] of Object.entries(ptoTasksEstimates)) {
        if (!mergedEstimates[dateStr]) {
          mergedEstimates[dateStr] = {};
        }
        for (const [employeeName, estimates] of Object.entries(employees)) {
          if (!mergedEstimates[dateStr][employeeName]) {
            mergedEstimates[dateStr][employeeName] = createEmptyCategorizedEstimates();
          }
          // Merge PTO estimates with existing estimates
          for (const [category, value] of Object.entries(estimates)) {
            mergedEstimates[dateStr][employeeName][category as keyof CategorizedEstimates] += value;
          }
        }
      }

      return mergedEstimates;
    });

    // Process overcapacity checks
    await io.runTask('check-overcapacity-violations', async () => {
      const ClickUpClient = (await import('@/data/clickup-client')).default;
      const clickupClient = new ClickUpClient();

      // Get error notifications to avoid duplicate tasks
      const supabase = createClient();
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const { data: errorNotifications } = await supabase
        .from('error_notifications')
        .select('*')
        .gte('last_sent', sevenDaysAgo.toISOString());

      // Process each date and employee
      for (const [dateStr, employees] of Object.entries(estimatedOpenTasks)) {
        for (const [employeeName, estimates] of Object.entries(employees)) {
          // Get working hours for this employee on this date
          const workingHours = workingHoursByEmployeeByDate?.[employeeName]?.[dateStr] || 0;

          // Calculate total estimated hours for the day
          const totalEstimatedHours = Object.values(estimates).reduce((sum, hours) => sum + hours, 0);

          // Get employee capacity settings
          const employeeSettings = overcapacitySettings?.find(setting => {
            // We need to match by employee name - this might need adjustment based on your data structure
            return true; // For now, use default settings
          });

          // Use default settings if no specific settings found
          const capacitySettings = employeeSettings || {
            overcapacity_day_with_target_hours_h: 2,
            overcapacity_day_with_target_hours_and_pto_vacation_h: 0,
            overcapacity_day_with_target_hours_and_pto_sick_h: 0,
            overcapacity_day_with_target_hours_and_pto_compensation_h: 0,
            overcapacity_day_with_target_hours_and_pto_training_h: 0,
            overcapacity_day_with_target_hours_and_pto_military_cs_h: 0,
            overcapacity_day_with_target_hours_and_pto_accident_h: 0,
            overcapacity_day_with_target_hours_and_pto_parental_leave_h: 0,
            overcapacity_day_with_target_hours_and_pto_holiday_h: 0,
            overcapacity_day_with_target_hours_and_pto_other_h: 0,
            overcapacity_day_with_target_hours_and_pto_freetime_h: 0,
            overcapacity_day_without_target_hours_h: 0,
            warning_day_active: true,
          };

          // Calculate allowed capacity based on working hours and PTO types
          let allowedCapacity = workingHours;

          if (workingHours > 0) {
            // Day with target hours
            allowedCapacity += capacitySettings.overcapacity_day_with_target_hours_h;
            allowedCapacity += estimates.vacation * capacitySettings.overcapacity_day_with_target_hours_and_pto_vacation_h;
            allowedCapacity += estimates.sick * capacitySettings.overcapacity_day_with_target_hours_and_pto_sick_h;
            allowedCapacity += estimates.compensation * capacitySettings.overcapacity_day_with_target_hours_and_pto_compensation_h;
            allowedCapacity += estimates.training * capacitySettings.overcapacity_day_with_target_hours_and_pto_training_h;
            allowedCapacity += estimates.military * capacitySettings.overcapacity_day_with_target_hours_and_pto_military_cs_h;
            allowedCapacity += estimates.accident * capacitySettings.overcapacity_day_with_target_hours_and_pto_accident_h;
            allowedCapacity += estimates.parental * capacitySettings.overcapacity_day_with_target_hours_and_pto_parental_leave_h;
            allowedCapacity += estimates.holiday * capacitySettings.overcapacity_day_with_target_hours_and_pto_holiday_h;
            allowedCapacity += estimates.other * capacitySettings.overcapacity_day_with_target_hours_and_pto_other_h;
            allowedCapacity += estimates.freetime * capacitySettings.overcapacity_day_with_target_hours_and_pto_freetime_h;
          } else {
            // Day without target hours
            allowedCapacity = capacitySettings.overcapacity_day_without_target_hours_h;
          }

          // Log if total time exceeds 8.6 hours
          if (totalEstimatedHours > 8.6) {
            // Get project/list information for logging
            const projectInfo = await io.runTask(`get-project-info-${employeeName}-${dateStr}`, async () => {
              const { data: tasksData } = await supabase
                .from('tasks')
                .select('projects(name), clickup_list_id')
                .eq('employees.name', employeeName)
                .gte('clickup_due_date', new Date(dateStr).getTime())
                .lte('clickup_due_date', new Date(dateStr).getTime() + 24 * 60 * 60 * 1000)
                .gt('clickup_time_estimate', 0)
                .is('clickup_date_closed', null);

              const projectNames = tasksData?.map(task => task.projects?.name).filter(Boolean) || [];
              const listIds = tasksData?.map(task => task.clickup_list_id).filter(Boolean) || [];

              return {
                projectNames: [...new Set(projectNames)],
                listIds: [...new Set(listIds)]
              };
            });

            const projectNamesStr = projectInfo.projectNames.length > 0
              ? projectInfo.projectNames.join(', ')
              : projectInfo.listIds.join(', ');

            await io.logger.info(
              `Employee ${employeeName} exceeds 8.6 hours on ${dateStr}: ${totalEstimatedHours.toFixed(2)}h. ` +
              `Projects/Lists: ${projectNamesStr || 'No projects found'}`
            );
          }

          // Check if overcapacity and warnings are active
          if (capacitySettings.warning_day_active && totalEstimatedHours > allowedCapacity) {
            const messageHash = `${employeeName}#${dateStr}#overcapacity`;
            const errorAlreadySent = errorNotifications?.some(
              (en) => en.message_hash === messageHash && en.last_sent >= sevenDaysAgo.toISOString()
            );

            if (!errorAlreadySent) {
              await io.runTask(`create-overcapacity-task-${employeeName}-${dateStr}`, async () => {
                // Create task for Michel Giesser
                await clickupClient.createTask(String(globalSettings.pm_list_id), {
                  name: `pm: Overcapacity Warning - ${employeeName} - ${dateStr}`,
                  description: `Employee ${employeeName} is over capacity on ${dateStr}:

Working Hours: ${workingHours}h
Total Estimated Hours: ${totalEstimatedHours.toFixed(2)}h
Allowed Capacity: ${allowedCapacity.toFixed(2)}h
Overcapacity: ${(totalEstimatedHours - allowedCapacity).toFixed(2)}h

Breakdown:
- Regular tasks: ${estimates.regular.toFixed(2)}h
- Vacation: ${estimates.vacation.toFixed(2)}h
- Sick: ${estimates.sick.toFixed(2)}h
- Compensation: ${estimates.compensation.toFixed(2)}h
- Training: ${estimates.training.toFixed(2)}h
- Military/ZS: ${estimates.military.toFixed(2)}h
- Accident: ${estimates.accident.toFixed(2)}h
- Parental: ${estimates.parental.toFixed(2)}h
- Holiday: ${estimates.holiday.toFixed(2)}h
- Other: ${estimates.other.toFixed(2)}h
- Freetime: ${estimates.freetime.toFixed(2)}h

Please review and adjust workload accordingly.`,
                  assignees: [process.env.CLICKUP_ADMIN_USER_ID],
                  priority: 2,
                  time_estimate: 15 * 60 * 1000,
                  due_date: new Date().getTime(),
                });

                // Record notification to prevent duplicates
                await supabase
                  .from('error_notifications')
                  .upsert(
                    { message_hash: messageHash, last_sent: new Date().toISOString() },
                    { onConflict: 'message_hash' }
                  );
              });
            }
          }
        }
      }
    });
  },
});
